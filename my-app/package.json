{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "flatpickr": "^4.6.13", "googleapis": "^154.0.0", "next": "15.4.1", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "pg": "^8.16.3", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/flatpickr": "^3.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}