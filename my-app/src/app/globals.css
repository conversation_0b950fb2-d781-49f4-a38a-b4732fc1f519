@import "tailwindcss";
@import "flatpickr/dist/flatpickr.min.css";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-dark: #1c2434;
  --color-dark-2: #24303f;
  --color-dark-3: #313d4a;
  --color-dark-4: #5d6679;
  --color-dark-6: #8a99af;
  --color-gray-dark: #24303f;
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #60a5fa;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Responsive utilities */
.container-responsive {
  @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Mobile-first responsive breakpoints */
@media (max-width: 640px) {
  .container-responsive {
    @apply px-3;
  }
}

/* Touch-friendly interactive elements */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
}

/* Smooth scrolling for mobile */
@media (max-width: 768px) {
  html {
    scroll-behavior: smooth;
  }
}

/* Prevent zoom on input focus for iOS */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select,
  textarea,
  input[type="text"],
  input[type="password"],
  input[type="datetime"],
  input[type="datetime-local"],
  input[type="date"],
  input[type="month"],
  input[type="time"],
  input[type="week"],
  input[type="number"],
  input[type="email"],
  input[type="url"],
  input[type="search"],
  input[type="tel"],
  input[type="color"] {
    font-size: 16px;
  }
}

.custom-gradient-1 {
  background: linear-gradient(135deg, #ec8686 0%, #ce0303 100%);
}

.text-dark {
  color: var(--color-dark);
}

.text-dark-4 {
  color: var(--color-dark-4);
}

.text-dark-6 {
  color: var(--color-dark-6);
}

.bg-gray-dark {
  background-color: var(--color-gray-dark);
}

.bg-dark-2 {
  background-color: var(--color-dark-2);
}

.shadow-1 {
  box-shadow: 0px 4px 12px rgba(16, 24, 40, 0.15), 0px 2px 6px rgba(16, 24, 40, 0.1);
}

.shadow-card {
  box-shadow: 0px 8px 25px rgba(16, 24, 40, 0.2), 0px 4px 10px rgba(16, 24, 40, 0.15);
}
