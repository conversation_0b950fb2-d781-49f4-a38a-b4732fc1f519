generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                           String                @id @default(cuid())
  email                        String                @unique
  password                     String?
  name                         String?
  image                        String?
  emailVerified                DateTime?
  createdAt                    DateTime              @default(now())
  updatedAt                    DateTime              @updatedAt
  cin                          String?               @unique
  nom                          String?
  prenom                       String?
  telephone                    String?
  dateDelivrance               DateTime?
  lieuDelivrance               String?
  address                      String?
  nationalite                  String?
  civilite                     String?
  dateNaissance                DateTime?
  gouvernorat                  String?
  specialite                   String?
  role                         UserRole              @default(CANDIDAT)
  disponibilites               Disponibilite[]       @relation("EnseignantDisponibilites")
  evaluationsAsCandidat        InterviewEvaluation[] @relation("EvaluationCandidat")
  evaluationsAsEnseignant      InterviewEvaluation[] @relation("EvaluationEnseignant")
  interviewRequestsSent        InterviewRequest[]    @relation("AdminInterviewRequests")
  interviewRequestsAsCandidate InterviewRequest[]    @relation("CandidateInterviewRequests")
  interviewRequestsReceived    InterviewRequest[]    @relation("EnseignantInterviewRequests")
  notifications                Notification[]
  reservations                 Reservation[]         @relation("CandidatReservations")
}

model Disponibilite {
  id            String        @id @default(cuid())
  id_Enseignant String
  dateDebut     DateTime
  dateFin       DateTime
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  enseignant    User          @relation("EnseignantDisponibilites", fields: [id_Enseignant], references: [id], onDelete: Cascade)
  reservations  Reservation[] @relation("DisponibiliteReservations")
}

model Reservation {
  id               String               @id @default(cuid())
  id_Candidat      String
  id_Disponibilite String
  status           ReservationStatus    @default(EN_ATTENTE)
  createdAt        DateTime             @default(now())
  updatedAt        DateTime             @updatedAt
  result           ReservationResult    @default(EN_ATTENTE)
  meetLink         String?
  evaluation       InterviewEvaluation?
  candidat         User                 @relation("CandidatReservations", fields: [id_Candidat], references: [id], onDelete: Cascade)
  disponibilite    Disponibilite        @relation("DisponibiliteReservations", fields: [id_Disponibilite], references: [id], onDelete: Cascade)
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  type      String
  message   String
  link      String?
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model InterviewRequest {
  id            String                 @id @default(cuid())
  adminId       String
  enseignantId  String
  candidateId   String
  status        InterviewRequestStatus @default(PENDING)
  dateEntretien DateTime?
  meetLink      String?
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @updatedAt
  admin         User                   @relation("AdminInterviewRequests", fields: [adminId], references: [id], onDelete: Cascade)
  candidate     User                   @relation("CandidateInterviewRequests", fields: [candidateId], references: [id], onDelete: Cascade)
  enseignant    User                   @relation("EnseignantInterviewRequests", fields: [enseignantId], references: [id], onDelete: Cascade)
}

model InterviewEvaluation {
  id              String      @id @default(cuid())
  reservationId   String      @unique
  enseignantId    String
  candidatId      String
  francais        Int?
  anglais         Int?
  motivation      Int?
  cultureGenerale Int?
  bonus           Int?
  noteSur100      Int?
  observation     String?
  competence      Competence?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  candidat        User        @relation("EvaluationCandidat", fields: [candidatId], references: [id], onDelete: Cascade)
  enseignant      User        @relation("EvaluationEnseignant", fields: [enseignantId], references: [id], onDelete: Cascade)
  reservation     Reservation @relation(fields: [reservationId], references: [id], onDelete: Cascade)
}

enum UserRole {
  ADMIN
  ENSEIGNANT
  CANDIDAT
  ETUDIANT
}

enum ReservationStatus {
  EN_ATTENTE
  CONFIRMEE
  ANNULEE
  TERMINEE
}

enum ReservationResult {
  EN_ATTENTE
  ACCEPTER
  REFUSER
}

enum InterviewRequestStatus {
  PENDING
  ACCEPTED
  REJECTED
  CANCELLED
}

enum Competence {
  CULTURE
  ART
  EXPERIENCE_ONG
  SPORT
  AUCUNE
}
